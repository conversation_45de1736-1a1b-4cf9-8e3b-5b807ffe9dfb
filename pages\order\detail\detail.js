// 订单详情页面
import API from '../../../utils/api.js';
import {
  ORDER_STATUS_NAMES,
  SERVICE_TYPE_NAMES,
  ACCEPTOR_LEVEL_NAMES
} from '../../../utils/constants.js';
import { buildQueryString } from '../../../utils/urlHelper.js';

const app = getApp();

Page({
  data: {
    orderId: '',
    orderInfo: null,
    userRole: '', // customer 或 accepter
    loading: true,
    refreshing: false,

    // 游戏昵称可见性控制
    gameNicknameVisible: false,

    // 状态流程
    statusFlow: [
      { key: 'pending', name: '待接单', icon: '⏳' },
      { key: 'accepted', name: '已接单', icon: '✅' },
      { key: 'in_progress', name: '进行中', icon: '🎮' },
      { key: 'completed', name: '已完成', icon: '🎉' }
    ],

    // 操作按钮配置
    actionButtons: [],

    // 遇到问题按钮显示控制
    showProblemButton: false,
    problemIcon: '', // 遇到问题按钮图标

    // 确保数据结构稳定
    order: null
  },

  // 已处理的通知ID集合
  processedNotifications: new Set(),

  onLoad(options) {
    // 保存原始方法
    this.originalShowLoading = app.utils.showLoading;
    this.originalWxShowLoading = wx.showLoading;

    // 临时禁用所有loading显示
    app.utils.showLoading = () => {};
    wx.showLoading = () => {};

    // 强制隐藏现有loading - 多次调用确保生效
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);
    setTimeout(() => wx.hideLoading(), 200);
    setTimeout(() => wx.hideLoading(), 500);

    if (options.id) {
      this.setData({
        orderId: options.id,
        loading: true
      });

      // 检查是否是从通知跳转过来的
      this.fromNotification = options.fromNotification === 'true';

      // 延迟加载，避免渲染冲突
      setTimeout(() => {
        this.loadOrderDetail();
        this.loadProblemIcon(); // 加载遇到问题按钮图标
      }, 100);

      // 设置实时通知监听，但如果是从通知跳转过来的，先清理通知再设置
      if (this.fromNotification) {
        // 从通知跳转过来的，先清理相关未读通知，然后再设置监听
        this.clearRelatedNotifications().then(() => {
          setTimeout(() => {
            this.setupRealtimeNotification();
          }, 1000);
        });
      } else {
        this.setupRealtimeNotification();
      }

      // 页面加载完成后，恢复原始的showLoading方法（延迟恢复，确保初始加载完成）
      setTimeout(() => {
        app.utils.showLoading = this.originalShowLoading;
        wx.showLoading = this.originalWxShowLoading;
      }, 3000);  // 延长到3秒，确保所有初始化完成
    } else {
      app.utils.showError('订单ID不能为空');
      wx.navigateBack();
    }
  },

  onShow() {
    console.log('=== 订单详情页显示 ===');

    // 临时禁用所有loading显示
    if (this.originalShowLoading) {
      app.utils.showLoading = () => {
        console.log('🚫 [订单详情显示] 阻止app.utils.showLoading调用');
      };
    }

    if (this.originalWxShowLoading) {
      wx.showLoading = () => {
        console.log('🚫 [订单详情显示] 阻止wx.showLoading调用');
      };
    }

    // 确保隐藏任何可能的系统加载提示
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);

    // 页面显示时刷新数据
    if (this.data.orderId) {
      this.loadOrderDetail();
    }

    // 页面显示完成后，恢复原始的showLoading方法
    setTimeout(() => {
      if (this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      if (this.originalWxShowLoading) {
        wx.showLoading = this.originalWxShowLoading;
      }
      console.log('✅ [订单详情显示] 已恢复原始showLoading方法');
    }, 2000);  // 延长到2秒
  },

  onUnload() {
    // 页面卸载时清理资源
    console.log('📋 [订单详情] 页面卸载');

    // 恢复原始的showLoading方法
    if (this.originalShowLoading) {
      app.utils.showLoading = this.originalShowLoading;
    }
    if (this.originalWxShowLoading) {
      wx.showLoading = this.originalWxShowLoading;
    }

    this.cleanupRealtimeNotification();
    // 清理已处理的通知记录
    this.processedNotifications.clear();
  },

  // 清理相关的未读通知
  async clearRelatedNotifications() {
    const { orderId } = this.data;
    const userInfo = app.globalData.userInfo;

    if (!orderId || !userInfo || !userInfo._id) {
      return;
    }

    try {
      console.log('📢 [通知清理] 清理订单相关未读通知:', orderId);

      // 将相关的未读通知标记为已读
      await wx.cloud.database().collection('notifications')
        .where({
          orderId: orderId,
          receiverId: userInfo._id,
          type: 'order_cancelled',
          status: 'unread'
        })
        .update({
          data: { status: 'read' }
        });

      console.log('📢 [通知清理] 通知清理完成');
    } catch (error) {
      console.error('📢 [通知清理] 清理失败:', error);
    }
  },

  // 设置实时通知监听
  setupRealtimeNotification() {
    const { orderId } = this.data;
    if (!orderId) return;

    // 获取当前用户信息
    const userInfo = app.globalData.userInfo;
    if (!userInfo || !userInfo._id) {
      console.log('📢 [实时通知] 用户信息不完整，跳过监听');
      return;
    }

    console.log('📢 [实时通知] 开始监听订单状态变化:', orderId, '用户ID:', userInfo._id);

    // 监听通知集合中的相关通知
    this.notificationWatcher = wx.cloud.database().collection('notifications')
      .where({
        orderId: orderId,
        receiverId: userInfo._id,
        type: 'order_cancelled',
        status: 'unread'
      })
      .watch({
        onChange: (snapshot) => {
          console.log('📢 [实时通知] 收到通知变化:', snapshot);

          // 处理新增的通知
          if (snapshot.docChanges) {
            snapshot.docChanges.forEach(change => {
              if (change.changeType === 'add') {
                const notificationId = change.doc._id;

                // 检查是否已经处理过这个通知
                if (this.processedNotifications.has(notificationId)) {
                  console.log('📢 [实时通知] 通知已处理，跳过:', notificationId);
                  return;
                }

                console.log('📢 [实时通知] 收到新的取消通知:', change.doc);
                this.processedNotifications.add(notificationId);
                this.showCancelNotification(change.doc);

                // 标记通知为已读
                wx.cloud.database().collection('notifications')
                  .doc(change.doc._id)
                  .update({
                    data: { status: 'read' }
                  });
              }
            });
          } else if (snapshot.docs && snapshot.docs.length > 0) {
            // 兼容旧版本的处理方式
            const notification = snapshot.docs[0];
            const notificationId = notification._id;

            // 检查是否已经处理过这个通知
            if (!this.processedNotifications.has(notificationId)) {
              this.processedNotifications.add(notificationId);
              this.showCancelNotification(notification);

              // 标记通知为已读
              wx.cloud.database().collection('notifications')
                .doc(notification._id)
                .update({
                  data: { status: 'read' }
                });
            }
          }
        },
        onError: (error) => {
          console.error('❌ [实时通知] 监听失败:', error);
        }
      });
  },

  // 清理实时通知监听
  cleanupRealtimeNotification() {
    if (this.notificationWatcher) {
      console.log('📢 [实时通知] 清理监听器');
      this.notificationWatcher.close();
      this.notificationWatcher = null;
    }
  },

  // 显示取消通知
  showCancelNotification(notification) {
    console.log('📢 [实时通知] 显示取消通知:', notification);

    // 检查当前页面是否就是目标订单的详情页面
    const currentOrderId = this.data.orderId;
    const notificationOrderId = notification.orderId;

    if (currentOrderId === notificationOrderId) {
      // 如果当前页面就是被取消订单的详情页面，只显示通知并刷新
      const title = notification.title || '订单已取消';
      const content = notification.content || '此订单已被取消';

      wx.showModal({
        title: title,
        content: content,
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#ff4d4f',
        success: () => {
          // 刷新订单详情
          this.loadOrderDetail();
        }
      });
    } else {
      // 如果不是当前订单，提供查看详情选项
      const title = notification.title || '订单已取消';
      const content = notification.content || '此订单已被取消';

      wx.showModal({
        title: title,
        content: content,
        showCancel: true,
        confirmText: '查看详情',
        cancelText: '我知道了',
        confirmColor: '#00d4ff',
        cancelColor: '#999999',
        success: (res) => {
          if (res.confirm) {
            // 跳转到订单详情页面，标记为从通知跳转
            wx.navigateTo({
              url: `/order-package/pages/detail/detail?id=${notificationOrderId}&fromNotification=true`
            });
          }
          // 无论选择什么都刷新当前页面
          this.loadOrderDetail();
        }
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 强制隐藏任何可能的系统加载框
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);

    this.setData({ refreshing: true });
    this.loadOrderDetail().finally(() => {
      // 强制隐藏任何可能的系统加载框
      wx.hideLoading();
      setTimeout(() => wx.hideLoading(), 10);

      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 加载订单详情
  async loadOrderDetail() {
    // 强制隐藏任何可能的系统加载框
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);

    this.setData({ loading: true });

    try {
      let orderData, userRole;



      // 获取订单详情
      const result = await API.getOrderDetail(this.data.orderId);

      if (result.success) {
        orderData = result.data;
        userRole = result.userRole;  // userRole 在根级别，不在 data 里
      } else {
        throw new Error(result.error || '加载失败');
      }

      const orderInfo = this.formatOrderData(orderData);



      const actionButtons = this.getActionButtons(orderInfo);
      const showProblemButton = this.shouldShowProblemButton(orderInfo, userRole);
      const gameNicknameVisible = this.checkGameNicknameVisibility(orderInfo, userRole);

      // 确保数据结构稳定
      this.setData({
        orderInfo: orderInfo || null,
        userRole: userRole || '',
        actionButtons: actionButtons || [],
        showProblemButton: showProblemButton,
        gameNicknameVisible: gameNicknameVisible,
        loading: false
      });

      // 强制隐藏任何可能的系统加载框
      wx.hideLoading();
      setTimeout(() => wx.hideLoading(), 10);
      setTimeout(() => wx.hideLoading(), 50);

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: `订单详情 - ${orderInfo.statusText}`
      });
    } catch (error) {
      console.error('加载订单详情失败:', error);

      // 强制隐藏任何可能的系统加载框
      wx.hideLoading();
      setTimeout(() => wx.hideLoading(), 10);

      // 设置错误状态
      this.setData({
        loading: false,
        orderInfo: null
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });

      // 延迟返回，让用户看到错误提示
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  // 清理文本内容，去除多余空格和换行符
  cleanTextContent(text) {
    if (!text) return '';
    // 将换行符替换为空格，然后去除多余的空格
    return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
  },

  // 格式化订单数据
  formatOrderData(order) {
    // 尝试从本地存储获取修改后的数据
    let modifiedData = {};
    try {
      const storedData = wx.getStorageSync(`create_order_${order._id}`);
      if (storedData) {
        modifiedData = storedData;
      }
    } catch (error) {
      console.warn('读取本地存储订单数据失败:', error);
    }

    // 合并修改后的数据
    const finalOrder = {
      ...order,
      title: modifiedData.title || order.title,
      content: modifiedData.content || order.content,
      reward: modifiedData.reward || order.reward || order.totalAmount,
      // 服务类型和时间信息
      serviceType: modifiedData.serviceType || order.serviceType,
      duration: modifiedData.duration || order.duration,
      rounds: modifiedData.rounds || order.rounds,
      tags: modifiedData.tags && modifiedData.tags.length > 0 ? modifiedData.tags : order.tags,
      orderType: modifiedData.orderType || order.orderType,
      scheduledDate: modifiedData.scheduledDate || order.scheduledDate,
      scheduledTime: modifiedData.scheduledTime || order.scheduledTime
    };

    return {
      ...finalOrder,
      // 清理文本内容
      content: this.cleanTextContent(finalOrder.content),
      title: this.cleanTextContent(finalOrder.title),
      statusText: ORDER_STATUS_NAMES[finalOrder.status] || finalOrder.status,
      statusIcon: this.getStatusIcon(finalOrder.status),
      serviceTypeText: finalOrder.serviceType ? (SERVICE_TYPE_NAMES[finalOrder.serviceType] || finalOrder.serviceType) : '任务服务',
      createTimeText: this.formatDateTime(finalOrder.createTime),
      acceptTimeText: finalOrder.acceptTime ? this.formatDateTime(finalOrder.acceptTime) : '',
      startTimeText: finalOrder.startTime ? this.formatDateTime(finalOrder.startTime) : '',
      endTimeText: finalOrder.endTime ? this.formatDateTime(finalOrder.endTime) : '',
      cancelledAtText: finalOrder.cancelledAt ? this.formatDateTime(finalOrder.cancelledAt) : '',
      scheduledTimeText: finalOrder.scheduledTime ? this.formatDateTime(finalOrder.scheduledTime) : '',
      tags: finalOrder.tags ? finalOrder.tags.map(tag => this.getTagLabel(tag)) : [],
      customerInfo: order.customerInfo || order.customer || null,
      acceptorInfo: order.acceptorInfo ? {
        ...order.acceptorInfo,
        levelText: ACCEPTOR_LEVEL_NAMES[order.acceptorInfo.level] || order.acceptorInfo.level || '普通'
      } : null,
      evaluation: order.evaluation ? {
        ...order.evaluation,
        createTimeText: this.formatDateTime(order.evaluation.customerEvaluationTime || order.evaluation.createTime)
      } : null,
      currentStatusIndex: this.getCurrentStatusIndex(finalOrder.status)
    };
  },

  // 获取当前状态在流程中的位置
  getCurrentStatusIndex(status) {
    const index = this.data.statusFlow.findIndex(item => item.key === status);
    return index >= 0 ? index : 0;
  },

  // 获取状态图标
  getStatusIcon(status) {
    const iconMap = {
      'pending': '⏳',
      'waiting_match': '⏳',
      'accepted': '✅',
      'in_progress': '🎮',
      'completed': '🎉',
      'cancelled': '❌'
    };
    return iconMap[status] || '⏳';
  },

  // 获取标签显示文本
  getTagLabel(tagValue) {
    const tagMap = {
      'high_winrate': '高胜率',
      'voice_chat': '可语音',
      'humorous': '幽默风趣',
      'professional': '专业指导',
      'newbie_friendly': '新手友好',
      'urgent': '急单'
    };
    return tagMap[tagValue] || tagValue;
  },

  // 判断是否显示"遇到问题"按钮
  shouldShowProblemButton(order, userRole) {
    if (!order || !userRole) {
      return false;
    }

    const { status } = order;

    // 在"已接单"和"进行中"状态才显示
    if (status !== 'accepted' && status !== 'in_progress') {
      return false;
    }

    // 接单者：在已接单和进行中状态显示
    if (userRole === 'accepter' || userRole === 'acceptor') {
      return true;
    }

    // 发单者：在已接单和进行中且已被接单时显示
    if (userRole === 'customer') {
      // 检查是否有接单者（订单已被接单）
      return !!(order.accepterId || order.acceptorInfo);
    }

    return false;
  },

  // 检查游戏昵称可见性
  checkGameNicknameVisibility(order, userRole) {
    console.log('🎮 [游戏昵称] 检查可见性:', {
      hasOrder: !!order,
      hasCustomerInfo: !!order?.customerInfo,
      hasGameNickName: !!order?.customerInfo?.gameNickName,
      gameNickName: order?.customerInfo?.gameNickName,
      userRole: userRole,
      orderStatus: order?.status
    });

    if (!order || !order.customerInfo || !order.customerInfo.gameNickName) {
      console.log('🎮 [游戏昵称] 不显示：缺少必要数据');
      return false;
    }

    // 如果是订单发布者自己，总是可见
    if (userRole === 'customer') {
      console.log('🎮 [游戏昵称] 显示：发布者自己');
      return true;
    }

    // 如果是接单者，需要检查订单状态
    if (userRole === 'accepter' || userRole === 'acceptor') {
      const visible = ['accepted', 'in_progress', 'completed'].includes(order.status);
      console.log('🎮 [游戏昵称] 接单者可见性:', visible, '状态:', order.status);
      return visible;
    }

    // 其他情况（如游客）不可见
    console.log('🎮 [游戏昵称] 不显示：游客或其他角色');
    return false;
  },

  // 复制游戏昵称
  copyGameNickname() {
    const gameNickName = this.data.orderInfo?.customerInfo?.gameNickName;
    if (!gameNickName) {
      wx.showToast({
        title: '游戏昵称不存在',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: gameNickName,
      success: () => {
        wx.showToast({
          title: '游戏昵称已复制',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 获取操作按钮
  getActionButtons(order) {
    const buttons = [];
    const { status } = order;
    const { userRole } = this.data;

    // 判断是否为订单发布者
    const isOrderOwner = this.isOrderOwner(order);

    if (userRole === 'customer') {
      // 客户操作
      switch (status) {
        case 'pending':
        case 'waiting_match':
          if (isOrderOwner) {
            // 如果是订单发布者，显示修改和取消按钮
            buttons.push({
              text: '修改订单',
              type: 'edit',
              style: 'default'
            });
            buttons.push({
              text: '取消订单',
              type: 'cancel',
              style: 'danger'
            });
          } else {
            // 如果不是订单发布者，显示抢单按钮
            buttons.push({
              text: '立即抢单',
              type: 'grab',
              style: 'primary'
            });
          }
          break;
        case 'accepted':
          buttons.push({
            text: '联系接单者',
            type: 'contact',
            style: 'primary'
          });
          buttons.push({
            text: '取消订单',
            type: 'cancel',
            style: 'default'
          });
          break;
        case 'in_progress':
          buttons.push({
            text: '进入聊天',
            type: 'chat',
            style: 'primary'
          });
          break;
        case 'completed':
          // 客户评价接单者
          if (!order.evaluation?.customerRating) {
            buttons.push({
              text: '评价接单者',
              type: 'evaluate',
              style: 'primary'
            });
          } else {
            buttons.push({
              text: '查看我的评价',
              type: 'viewMyEvaluation',
              style: 'default'
            });
          }

          // 显示接单者对客户的评价（如果有）
          if (order.evaluation?.accepterRating) {
            buttons.push({
              text: '查看收到的评价',
              type: 'viewReceivedEvaluation',
              style: 'default'
            });
          }

          buttons.push({
            text: '重新下单',
            type: 'reorder',
            style: 'default'
          });
          break;
      }
    } else if (userRole === 'acceptor' || userRole === 'accepter') {
      // 接单者操作
      switch (status) {
        case 'pending':
          buttons.push({
            text: '接受订单',
            type: 'accept',
            style: 'primary'
          });
          break;
        case 'accepted':
          buttons.push({
            text: '开始服务',
            type: 'start',
            style: 'primary'
          });
          buttons.push({
            text: '联系客户',
            type: 'contact',
            style: 'default'
          });
          break;
        case 'in_progress':
          buttons.push({
            text: '完成订单',
            type: 'complete',
            style: 'primary'
          });
          buttons.push({
            text: '进入聊天',
            type: 'chat',
            style: 'default'
          });
          break;
        case 'completed':
          // 接单者评价客户
          if (!order.evaluation?.accepterRating) {
            buttons.push({
              text: '评价客户',
              type: 'evaluate',
              style: 'primary'
            });
          } else {
            buttons.push({
              text: '查看我的评价',
              type: 'viewMyEvaluation',
              style: 'default'
            });
          }

          // 显示客户对接单者的评价（如果有）
          if (order.evaluation?.customerRating) {
            buttons.push({
              text: '查看收到的评价',
              type: 'viewReceivedEvaluation',
              style: 'default'
            });
          }
          break;
      }
    } else if (userRole === 'viewer') {
      // 查看者（非订单相关用户）操作
      switch (status) {
        case 'pending':
        case 'waiting_match':
          // 待接单状态，显示抢单按钮
          buttons.push({
            text: '立即抢单',
            type: 'grab',
            style: 'primary'
          });
          break;
        // 其他状态不显示操作按钮，只能查看
      }
    }

    return buttons;
  },

  // 处理按钮点击
  handleAction(e) {
    const { type } = e.currentTarget.dataset;

    switch (type) {
      case 'accept':
        this.acceptOrder();
        break;
      case 'grab':
        this.grabOrder();
        break;
      case 'start':
        this.startService();
        break;
      case 'complete':
        this.completeOrder();
        break;
      case 'cancel':
        this.cancelOrder();
        break;
      case 'contact':
        this.contactUser();
        break;
      case 'chat':
        this.createOrEnterChatRoom();
        break;
      case 'evaluate':
        this.evaluateOrder();
        break;
      case 'viewMyEvaluation':
        this.viewMyEvaluation();
        break;
      case 'viewReceivedEvaluation':
        this.viewReceivedEvaluation();
        break;
      case 'reorder':
        this.reorder();
        break;
      case 'edit':
        this.editOrder();
        break;
    }
  },

  // 处理"遇到问题"按钮点击
  handleProblem() {
    const { orderInfo, userRole } = this.data;

    console.log('🔍 [遇到问题] 当前数据状态:', {
      hasOrderInfo: !!orderInfo,
      userRole: userRole,
      orderStatus: orderInfo?.status,
      orderId: orderInfo?._id
    });

    if (!orderInfo) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
      return;
    }

    // 如果 userRole 为空，尝试重新判断
    let finalUserRole = userRole;
    if (!finalUserRole) {
      console.log('⚠️ [遇到问题] userRole 为空，尝试重新判断...');
      finalUserRole = this.determineUserRole(orderInfo);
      console.log('🔍 [遇到问题] 重新判断的 userRole:', finalUserRole);

      // 更新 data 中的 userRole
      this.setData({ userRole: finalUserRole });
    }

    // 确认对话框
    const isAccepter = finalUserRole === 'accepter' || finalUserRole === 'acceptor';
    const roleText = isAccepter ? '接单者' : '发单者';

    wx.showModal({
      title: '遇到问题',
      content: `作为${roleText}，您确定要取消此订单吗？取消后订单状态将变为"已取消"，此操作无法撤销。`,
      confirmText: '确定取消',
      cancelText: '再想想',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.cancelOrderWithProblem();
        }
      }
    });
  },

  // 判断用户角色
  determineUserRole(orderInfo) {
    const userInfo = app.globalData.userInfo;
    if (!userInfo || !userInfo._id) {
      return 'viewer';
    }

    const isCustomer = String(orderInfo.customerId) === String(userInfo._id);
    const accepterId = orderInfo.accepterId || orderInfo.companionId;
    const isAccepter = accepterId && String(accepterId) === String(userInfo._id);

    console.log('🔍 [角色判断] 详细信息:', {
      currentUserId: userInfo._id,
      orderCustomerId: orderInfo.customerId,
      orderAccepterId: orderInfo.accepterId,
      orderCompanionId: orderInfo.companionId,
      isCustomer,
      isAccepter
    });

    if (isCustomer) {
      return 'customer';
    } else if (isAccepter) {
      return 'accepter';
    } else {
      return 'viewer';
    }
  },

  // 通用取消原因选择函数
  showCancelReasonSelector(callback) {
    wx.showActionSheet({
      itemList: ['临时有事', '价格问题', '服务质量问题', '沟通问题', '其他问题'],
      success: (res) => {
        const reasons = ['临时有事', '价格问题', '服务质量问题', '沟通问题', '其他问题'];
        const selectedReason = reasons[res.tapIndex];

        if (selectedReason === '其他问题') {
          // 显示输入框让用户输入具体原因
          wx.showModal({
            title: '请输入取消原因',
            content: '请详细说明取消原因：',
            editable: true,
            placeholderText: '请输入具体的取消原因...',
            success: (inputRes) => {
              if (inputRes.confirm) {
                const customReason = inputRes.content.trim();
                if (customReason) {
                  callback(customReason);
                } else {
                  wx.showToast({
                    title: '请输入取消原因',
                    icon: 'none'
                  });
                }
              }
            }
          });
        } else {
          callback(selectedReason);
        }
      }
    });
  },

  // 因遇到问题取消订单
  async cancelOrderWithProblem() {
    this.showCancelReasonSelector(async (reason) => {
      // 确认取消
      wx.showModal({
        title: '确认取消订单',
        content: `确定要取消此订单吗？\n取消原因：${reason}`,
        confirmText: '确定取消',
        cancelText: '再想想',
        confirmColor: '#ff4d4f',
        success: async (modalRes) => {
          if (modalRes.confirm) {
            try {
              app.utils.showLoading('正在取消订单...');

              const result = await API.callFunction('cancelOrder', {
                orderId: this.data.orderId,
                reason: reason,
                userRole: this.data.userRole
              });

                if (result.success) {
                  app.utils.showSuccess('订单已取消');

                  // 检查通知是否创建成功
                  setTimeout(async () => {
                    try {
                      console.log('🔍 [通知检查] 先初始化通知集合...');

                      // 先初始化通知集合
                      const initResult = await API.callFunction('initNotifications', {});
                      console.log('🔍 [通知检查] 初始化结果:', initResult);

                      console.log('🔍 [通知检查] 检查通知是否创建成功...');
                      const notificationResult = await wx.cloud.database().collection('notifications')
                        .where({
                          orderId: this.data.orderId,
                          type: 'order_cancelled'
                        })
                        .orderBy('createTime', 'desc')
                        .limit(1)
                        .get();

            console.log('🔍 [通知检查] 查询结果:', notificationResult);
            if (notificationResult.data.length > 0) {
              const notification = notificationResult.data[0];
              console.log('✅ [通知检查] 找到通知记录:', notification);

              // 检查通知的接收者ID
              const app = getApp();
              const currentUserId = app.globalData.userInfo?._id;
              console.log('🔍 [通知检查] 通知接收者检查:', {
                notificationReceiverId: notification.receiverId,
                currentUserId: currentUserId,
                isMatch: notification.receiverId === currentUserId,
                notificationStatus: notification.status
              });

              // 检查全局监听器状态
              console.log('🔍 [监听器检查] 全局监听器状态:', {
                hasWatcher: !!app.globalNotificationWatcher,
                userInfo: app.globalData.userInfo,
                cloudReady: app.globalData.cloudReady
              });

              // 手动触发全局监听器检查
              if (app.setupGlobalNotificationWatcher) {
                console.log('🔍 [监听器检查] 手动重启全局监听器...');
                app.setupGlobalNotificationWatcher();
              }
            } else {
              console.log('❌ [通知检查] 未找到通知记录');
            }
          } catch (error) {
            console.error('❌ [通知检查] 查询失败:', error);
          }
        }, 1000);

                // 刷新订单详情
                setTimeout(() => {
                  this.loadOrderDetail();
                }, 1500);

              } else {
                console.error('取消订单失败:', result);
                app.utils.showError(result.error || '取消失败，请重试');
              }

            } catch (error) {
              console.error('取消订单异常:', error);
              app.utils.showError('网络错误，请重试');
            } finally {
              app.utils.hideLoading();
            }
          }
        }
      });
    });
  },

  // 接受订单
  async acceptOrder() {
    wx.showModal({
      title: '确认接单',
      content: '确定要接受这个订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            app.utils.showLoading('处理中...');
            const result = await API.acceptOrder(this.data.orderId);

            if (result.success) {
              app.utils.showSuccess('接单成功');
              this.loadOrderDetail();
            } else {
              app.utils.showError(result.error || '接单失败');
            }
          } catch (error) {
            console.error('接单失败:', error);
            app.utils.showError('接单失败，请重试');
          } finally {
            app.utils.hideLoading();
          }
        }
      }
    });
  },

  // 开始服务
  async startService() {
    // 先检查订单状态和权限
    console.log('开始服务 - 当前订单信息:', {
      orderId: this.data.orderId,
      status: this.data.orderInfo?.status,
      accepterId: this.data.orderInfo?.accepterId,
      customerId: this.data.orderInfo?.customerId
    });

    // 检查当前用户信息
    console.log('当前用户角色:', this.data.userRole);
    console.log('当前用户信息:', app.globalData.userInfo);

    // 显示接单者注意事项
    const noticeContent = '1.全程录屏或截图记录服务过程\n2.保存与客户的所有聊天记录  \n3.记录关键时间节点和操作    \n4.遇违规要求及时截图保存证据\n5.保持专业友好的服务态度   \n6.严格按订单要求提供服务   \n7.积极与顾客沟通细节      \n8.纠纷通过平台客服处理    ';

    wx.showModal({
      title: '开始服务',
      content: noticeContent,
      confirmText: '确认开始',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          try {
            app.utils.showLoading('处理中...');
            // 调用开始服务API
            const result = await API.startService(this.data.orderId);
            console.log('开始服务API返回结果:', result);

            if (result.success) {
              app.utils.showSuccess('服务已开始');
              this.loadOrderDetail();
            } else {
              console.error('开始服务失败 - 详细信息:', result);

              // 检查订单状态是否实际已经更新
              await this.loadOrderDetail();

              // 如果订单状态已经是 in_progress，说明实际上成功了
              if (this.data.orderInfo && this.data.orderInfo.status === 'in_progress') {
                app.utils.showSuccess('服务已开始');
                return;
              }

              app.utils.showError(result.error || '操作失败');
            }
          } catch (error) {
            console.error('开始服务失败:', error);
            app.utils.showError('操作失败，请重试');
          } finally {
            app.utils.hideLoading();
          }
        }
      }
    });
  },

  // 完成订单
  async completeOrder() {
    // 先检查订单状态和权限
    console.log('完成订单 - 当前订单信息:', {
      orderId: this.data.orderId,
      status: this.data.order?.status,
      accepterId: this.data.order?.accepterId,
      customerId: this.data.order?.customerId
    });

    wx.showModal({
      title: '完成订单',
      content: '确认订单已完成吗？完成后将无法撤销。',
      success: async (res) => {
        if (res.confirm) {
          try {
            app.utils.showLoading('处理中...');
            // 调用完成订单API
            const result = await API.completeOrder(this.data.orderId);

            if (result.success) {
              app.utils.showSuccess('订单已完成');
              this.loadOrderDetail();
            } else {
              console.error('完成订单失败 - 详细信息:', result);
              app.utils.showError(result.error || '操作失败');
            }
          } catch (error) {
            console.error('完成订单失败:', error);
            app.utils.showError('操作失败，请重试');
          } finally {
            app.utils.hideLoading();
          }
        }
      }
    });
  },

  // 取消订单
  cancelOrder() {
    console.log('cancelOrder 方法被调用');
    this.showCancelReasonSelector(async (reason) => {
      wx.showModal({
        title: '确认取消',
        content: `确定要取消订单吗？\n取消原因：${reason}`,
        success: async (modalRes) => {
          console.log('用户确认取消:', modalRes.confirm);
          if (modalRes.confirm) {
            try {
              console.log('开始取消订单:', this.data.orderId, reason);
              app.utils.showLoading('处理中...');

              // 确定用户角色
              const userInfo = app.globalData.userInfo;
              const orderInfo = this.data.orderInfo;
              let userRole = 'customer'; // 默认为发单者

              if (orderInfo && userInfo) {
                // 如果当前用户是接单者
                if (orderInfo.accepterId === userInfo._id) {
                  userRole = 'accepter';
                }
                // 如果当前用户是发单者
                else if (orderInfo.customerId === userInfo._id) {
                  userRole = 'customer';
                }
              }

              console.log('🔍 [订单详情-取消订单] 用户角色判断:', {
                orderId: this.data.orderId,
                currentUserId: userInfo?._id,
                orderCustomerId: orderInfo?.customerId,
                orderAccepterId: orderInfo?.accepterId,
                determinedRole: userRole
              });

              const result = await API.cancelOrder(this.data.orderId, reason, userRole);
              console.log('取消订单结果:', result);

              if (result.success) {
                app.utils.showSuccess('订单已取消');
                this.loadOrderDetail();
              } else {
                app.utils.showError(result.error || '取消失败');
              }
            } catch (error) {
              console.error('取消订单失败:', error);
              app.utils.showError('取消失败，请重试');
            } finally {
              app.utils.hideLoading();
            }
          }
        }
      });
    });
  },

  // 联系用户
  async contactUser() {
    const { orderInfo, userRole } = this.data;

    console.log('联系用户调试信息:', {
      userRole,
      hasAcceptorInfo: !!orderInfo.acceptorInfo,
      hasAccepterInfo: !!orderInfo.accepterInfo,
      hasCustomerInfo: !!orderInfo.customerInfo,
      hasCustomer: !!orderInfo.customer
    });

    if (userRole === 'customer' && (orderInfo.acceptorInfo || orderInfo.accepterInfo)) {
      // 客户联系接单者
      await this.createOrEnterChatRoom();
    } else if ((userRole === 'acceptor' || userRole === 'accepter') && (orderInfo.customerInfo || orderInfo.customer)) {
      // 接单者联系客户
      await this.createOrEnterChatRoom();
    } else {
      app.utils.showError('无法联系对方，订单信息不完整');
    }
  },

  // 创建或进入聊天室
  async createOrEnterChatRoom() {
    const { orderInfo } = this.data;

    try {
      if (orderInfo.chatRoomId) {
        // 聊天室已存在，直接进入
        this.enterChatRoom();
      } else {
        // 聊天室不存在，直接创建新的聊天室
        app.utils.showLoading('创建聊天室...');
        const result = await API.createChatRoom(this.data.orderId);

        if (result.success) {
          // 更新订单信息中的聊天室ID
          this.setData({
            'orderInfo.chatRoomId': result.data.chatRoomId
          });

          // 进入聊天室
          wx.navigateTo({
            url: `/chat-package/pages/room/room?roomId=${result.data.chatRoomId}&orderId=${this.data.orderId}`
          });
        } else {
          app.utils.showError(result.error || '创建聊天室失败');
        }
      }
    } catch (error) {
      console.error('创建或进入聊天室失败:', error);
      app.utils.showError('操作失败，请重试');
    } finally {
      app.utils.hideLoading();
    }
  },

  // 进入聊天室
  enterChatRoom() {
    const { orderInfo } = this.data;

    if (orderInfo.chatRoomId) {
      wx.navigateTo({
        url: `/chat-package/pages/room/room?roomId=${orderInfo.chatRoomId}&orderId=${this.data.orderId}`
      });
    } else {
      app.utils.showError('聊天室不存在');
    }
  },

  // 评价订单
  evaluateOrder() {
    wx.navigateTo({
      url: `/order-package/pages/evaluation/evaluation?orderId=${this.data.orderId}`
    });
  },

  // 查看我的评价
  viewMyEvaluation() {
    const { orderInfo, userRole } = this.data;
    const evaluation = orderInfo.evaluation;

    console.log('=== 查看我的评价调试 ===');
    console.log('userRole:', userRole);
    console.log('evaluation:', evaluation);
    console.log('evaluation存在:', !!evaluation);

    if (evaluation) {
      console.log('customerRating:', evaluation.customerRating);
      console.log('accepterRating:', evaluation.accepterRating);
    }

    if (!evaluation) {
      app.utils.showError('暂无评价信息');
      return;
    }

    let myEvaluation = null;
    if (userRole === 'customer' && evaluation.customerRating) {
      myEvaluation = {
        rating: evaluation.customerRating,
        tags: evaluation.customerTags || [],
        content: evaluation.customerContent || '',
        evaluationTime: evaluation.customerEvaluationTime,
        targetRole: '接单者'
      };
      console.log('找到客户评价:', myEvaluation);
    } else if ((userRole === 'accepter' || userRole === 'acceptor') && evaluation.accepterRating) {
      myEvaluation = {
        rating: evaluation.accepterRating,
        tags: evaluation.accepterTags || [],
        content: evaluation.accepterContent || '',
        evaluationTime: evaluation.accepterEvaluationTime,
        targetRole: '客户'
      };
      console.log('找到接单者评价:', myEvaluation);
    }

    if (myEvaluation) {
      // 为我的评价添加自己的信息作为评价者信息
      const { orderInfo, userRole } = this.data;
      if (userRole === 'customer') {
        myEvaluation.evaluatorInfo = orderInfo.customerInfo || orderInfo.customer || {
          nickName: '我',
          avatarUrl: '/images/default-avatar.png'
        };
      } else {
        myEvaluation.evaluatorInfo = orderInfo.accepterInfo || orderInfo.accepter || {
          nickName: '我',
          avatarUrl: '/images/default-avatar.png'
        };
      }
      this.showEvaluationDetailWithUser('我的评价', myEvaluation);
    } else {
      console.log('未找到评价，userRole:', userRole, 'evaluation:', evaluation);
      app.utils.showError('您还未评价此订单');
    }
  },

  // 查看收到的评价
  viewReceivedEvaluation() {
    const { orderInfo, userRole } = this.data;
    const evaluation = orderInfo.evaluation;

    if (!evaluation) {
      app.utils.showError('暂无评价信息');
      return;
    }

    let receivedEvaluation = null;
    if (userRole === 'customer' && evaluation.accepterRating) {
      // 客户查看接单者的评价
      receivedEvaluation = {
        rating: evaluation.accepterRating,
        tags: evaluation.accepterTags || [],
        content: evaluation.accepterContent || '',
        evaluationTime: evaluation.accepterEvaluationTime,
        fromRole: '接单者',
        evaluatorInfo: orderInfo.accepterInfo || orderInfo.accepter || {
          nickName: '接单者',
          avatarUrl: '/images/default-avatar.png'
        }
      };
    } else if ((userRole === 'accepter' || userRole === 'acceptor') && evaluation.customerRating) {
      // 接单者查看客户的评价
      receivedEvaluation = {
        rating: evaluation.customerRating,
        tags: evaluation.customerTags || [],
        content: evaluation.customerContent || '',
        evaluationTime: evaluation.customerEvaluationTime,
        fromRole: '客户',
        evaluatorInfo: orderInfo.customerInfo || orderInfo.customer || {
          nickName: '客户',
          avatarUrl: '/images/default-avatar.png'
        }
      };
    }

    if (receivedEvaluation) {
      this.showEvaluationDetailWithUser('收到的评价', receivedEvaluation);
    } else {
      app.utils.showError('暂无收到的评价');
    }
  },

  // 显示评价详情弹窗（简单版本，用于查看自己的评价）
  showEvaluationDetail(title, evaluation) {
    const ratingTexts = ['', '很差', '较差', '一般', '满意', '非常满意'];
    const stars = '★'.repeat(evaluation.rating) + '☆'.repeat(5 - evaluation.rating);

    let content = `${stars} ${ratingTexts[evaluation.rating]}\n\n`;

    if (evaluation.tags && evaluation.tags.length > 0) {
      content += `标签：${evaluation.tags.join('、')}\n\n`;
    }

    if (evaluation.content) {
      content += `评价内容：${evaluation.content}\n\n`;
    }

    if (evaluation.evaluationTime) {
      const time = new Date(evaluation.evaluationTime).toLocaleString();
      content += `评价时间：${time}`;
    }

    wx.showModal({
      title: title,
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 显示带用户信息的评价详情
  showEvaluationDetailWithUser(title, evaluation) {
    // 跳转到评价详情页面
    const evaluationData = encodeURIComponent(JSON.stringify(evaluation));
    wx.navigateTo({
      url: `/pages/evaluation/detail/detail?title=${encodeURIComponent(title)}&data=${evaluationData}`
    });
  },

  // 格式化时间（用于评价详情页面）
  formatTime(time) {
    if (!time) return '';
    const date = new Date(time);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  // 获取评分文本（用于评价详情页面）
  getRatingText(rating) {
    const ratingTexts = ['', '很差', '较差', '一般', '满意', '非常满意'];
    return ratingTexts[rating] || '';
  },

  // 重新下单
  reorder() {
    const { orderInfo } = this.data;

    let url = '/order-package/pages/create/create';
    if (orderInfo.accepterInfo) {
      url += `?accepterId=${orderInfo.accepterInfo._id}`;
    }

    wx.navigateTo({ url });
  },

  // 格式化日期时间
  formatDateTime(dateStr) {
    if (!dateStr) return '';

    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hour}:${minute}`;
  },



  // 复制订单号
  copyOrderNo() {
    const { orderInfo } = this.data;
    if (orderInfo && orderInfo.orderNo) {
      wx.setClipboardData({
        data: orderInfo.orderNo,
        success: () => {
          app.utils.showSuccess('订单号已复制');
        }
      });
    }
  },

  // 查看接单者详情
  viewAcceptorDetail() {
    const { orderInfo } = this.data;
    if (orderInfo && orderInfo.acceptorInfo) {
      // 简化：直接进入聊天，不需要专门的接单者详情页
      this.enterChatRoom();
    }
  },

  // 抢单
  async grabOrder() {
    const { orderInfo } = this.data;

    // 防重复点击检查
    if (this.data.isGrabbing) {
      console.log('🚫 [防重复] 正在抢单中，忽略重复点击');
      return;
    }

    // 构建服务时间显示文本
    let serviceText = '';
    if (orderInfo.serviceType === 'rounds' || (orderInfo.rounds && !orderInfo.serviceType)) {
      serviceText = `局数：${orderInfo.rounds || 5}局`;
    } else {
      serviceText = `时长：${orderInfo.duration || 2}小时`;
    }

    wx.showModal({
      title: '确认抢单',
      content: `确定要抢这个订单吗？\n金额：¥${orderInfo.reward}\n${serviceText}`,
      confirmText: '确认抢单',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          // 设置抢单状态，防止重复点击
          this.setData({
            isGrabbing: true,
            loading: true  // 使用页面自定义加载状态
          });

          console.log('🎯 [详情页抢单] 开始抢单，订单ID:', this.data.orderId);

          try {
            // 调用抢单API
            const result = await API.grabOrder(this.data.orderId);

            // 处理抢单结果
            if (result.success) {
              // 重置抢单状态
              this.setData({
                isGrabbing: false,
                loading: false  // 关闭自定义加载状态
              });

              wx.showToast({
                title: '抢单成功！',
                icon: 'success',
                duration: 2000
              });

              // 刷新订单详情
              this.loadOrderDetail();
            } else {
              // 特殊情况：如果是ORDER_ALREADY_TAKEN，快速检查是否为当前用户抢到
              if (result.errorCode === 'ORDER_ALREADY_TAKEN') {
                console.log('🔍 [智能检查] ORDER_ALREADY_TAKEN，快速验证是否为当前用户抢到');

                try {
                  // 使用更快的直接数据库查询
                  const checkResult = await wx.cloud.database().collection('orders')
                    .doc(this.data.orderId)
                    .field({ accepterId: true, status: true })
                    .get();

                  if (checkResult.data &&
                      checkResult.data.accepterId === app.globalData.userInfo._id) {
                    console.log('✅ [智能检查] 确认是当前用户抢到，显示成功');

                    this.setData({
                      isGrabbing: false,
                      loading: false  // 关闭自定义加载状态
                    });

                    wx.showToast({
                      title: '抢单成功！',
                      icon: 'success',
                      duration: 2000
                    });

                    this.loadOrderDetail();
                    return;
                  }
                } catch (checkError) {
                  console.log('🔍 [智能检查] 检查失败，继续显示错误');
                }
              }

              console.log('❌ [抢单失败] 确认失败，显示错误信息');

              // 确实是抢单失败，处理错误信息
              let errorMessage = result.error || '抢单失败';
              let shouldRefreshDetail = false;

              // 优先使用错误代码进行判断
              if (result.errorCode) {
                switch (result.errorCode) {
                  case 'ORDER_ALREADY_TAKEN':
                  case 'CONCURRENT_CONFLICT':
                  case 'MAX_RETRIES_EXCEEDED':
                  case 'CONCURRENT_UPDATE_FAILED':
                  case 'VERIFICATION_FAILED':
                    errorMessage = '此订单已被其他用户抢走';
                    shouldRefreshDetail = true;
                    break;
                  case 'ORDER_CANCELLED':
                    errorMessage = '此订单已被取消';
                    shouldRefreshDetail = true;
                    break;
                  case 'ORDER_COMPLETED':
                    errorMessage = '此订单已完成';
                    shouldRefreshDetail = true;
                    break;
                  case 'ORDER_NOT_FOUND':
                    errorMessage = '订单不存在';
                    shouldRefreshDetail = true;
                    break;
                  case 'INVALID_STATUS':
                    errorMessage = '订单状态已变更，无法抢单';
                    shouldRefreshDetail = true;
                    break;
                  case 'OWN_ORDER':
                    errorMessage = '不能抢自己发布的订单';
                    break;
                  case 'USER_NOT_FOUND':
                    errorMessage = '用户信息异常，请重新登录';
                    break;
                  default:
                    // 使用原始错误消息
                    break;
                }
              } else {
                // 兼容旧版本：根据错误信息进行判断
                if (errorMessage.includes('不存在') ||
                    errorMessage.includes('已被') ||
                    errorMessage.includes('已取消') ||
                    errorMessage.includes('订单状态不允许接单') ||
                    errorMessage.includes('状态不允许') ||
                    errorMessage.includes('订单不可用')) {
                  errorMessage = '此订单已被抢或已取消';
                  shouldRefreshDetail = true;
                }
              }

              console.error('❌ [详情页抢单失败] 错误处理:', {
                originalError: result.error,
                errorCode: result.errorCode,
                finalMessage: errorMessage,
                shouldRefresh: shouldRefreshDetail
              });

              // 关闭自定义加载状态
              this.setData({
                isGrabbing: false,
                loading: false
              });

              wx.showToast({
                title: errorMessage,
                icon: 'error',
                duration: 2500
              });

              // 如果是订单状态相关的错误，刷新订单详情
              if (shouldRefreshDetail) {
                setTimeout(() => {
                  console.log('🔄 [详情页自动刷新] 由于订单状态错误，刷新订单详情');
                  this.loadOrderDetail();
                }, 1000);
              }
            }

          } catch (error) {
            console.error('抢单失败:', error);
            wx.showToast({
              title: '抢单失败，请重试',
              icon: 'error'
            });
          } finally {
            // 重置抢单状态，允许再次点击
            this.setData({
              isGrabbing: false,
              loading: false  // 确保关闭自定义加载状态
            });
          }
        }
      }
    });
  },

  // 修改订单
  editOrder() {
    const { orderInfo } = this.data;

    // 手动构建URL参数字符串（微信小程序不支持URLSearchParams）
    const params = {
      mode: 'edit',
      orderId: orderInfo._id,
      title: orderInfo.title || '',
      content: orderInfo.content || '',
      reward: orderInfo.reward || '',
      platformType: orderInfo.platformType || 'pc',
      serviceType: orderInfo.serviceType || 'duration',
      duration: orderInfo.duration || '',
      rounds: orderInfo.rounds || '',
      orderType: orderInfo.orderType || 'immediate',
      scheduledDate: orderInfo.scheduledDate || '',
      scheduledTime: orderInfo.scheduledTime || ''
    };

    console.log('🔍 [订单详情-编辑订单] 传递的参数:', params);

    // 构建查询字符串
    const queryString = buildQueryString(params);

    // 跳转到订单编辑页面
    wx.navigateTo({
      url: `/order-package/pages/create/create?${queryString}`
    });
  },

  // 判断是否为订单发布者
  isOrderOwner(order) {
    const userInfo = app.globalData.userInfo;

    console.log('isOrderOwner - 调试信息:', {
      currentUserOpenid: userInfo?.openid,
      orderCustomerOpenid: order.customerInfo?.openid,
      orderId: order._id
    });

    // 正确的逻辑：比较当前用户openid与订单发布者openid
    if (!userInfo || !userInfo.openid || !order.customerInfo || !order.customerInfo.openid) {
      console.log('缺少必要的用户信息，默认不是订单发布者');
      return false;
    }

    const isOwner = userInfo.openid === order.customerInfo.openid;
    console.log('是否为订单发布者:', isOwner);
    return isOwner;
  },

  // 加载遇到问题按钮图标
  async loadProblemIcon() {
    try {
      console.log('🔍 [订单详情] 开始加载遇到问题按钮图标...');

      // 使用云存储图标
      const cloudFileId = 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/icons/problem.png';

      const result = await wx.cloud.getTempFileURL({
        fileList: [cloudFileId]
      });

      if (result.fileList && result.fileList.length > 0 && result.fileList[0].status === 0) {
        const problemIcon = result.fileList[0].tempFileURL;
        this.setData({
          problemIcon
        });
        console.log('✅ [订单详情] 遇到问题按钮图标设置成功:', problemIcon);
      } else {
        throw new Error('云存储图标获取失败');
      }

    } catch (error) {
      console.error('❌ [订单详情] 加载遇到问题按钮图标失败:', error);
      // 设置空字符串，将使用CSS中的默认样式
      this.setData({
        problemIcon: ''
      });
    }
  }
});